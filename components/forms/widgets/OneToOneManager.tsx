"use client";

import React, { useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  Plus,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RelationshipConfig } from "@/lib/schema/FormGenerator";
import { DynamicFieldRenderer } from "./DynamicFieldRenderer";
import { Field, Schema } from "@/lib/schema/types";

interface OneToOneManagerProps {
  relationship: RelationshipConfig;
  fieldName: string;
  relatedSchema: Schema;
  className?: string;
  parentId?: string; // The ID of the parent record for reference_id
  relatedSchemas?: Record<string, Schema>;
}

export const OneToOneManager: React.FC<OneToOneManagerProps> = ({
  relationship,
  fieldName,
  relatedSchema,
  className = "",
  parentId,
  relatedSchemas = {},
}) => {
  const { watch, setValue } = useFormContext();
  const item = watch(fieldName) || null;
  const [isCollapsed, setIsCollapsed] = useState(false);

  const addItem = async () => {
    const newItem = await createEmptyItem(relatedSchema, parentId);
    setValue(fieldName, newItem, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  const updateItem = (updatedItem: any) => {
    setValue(fieldName, updatedItem, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  const createEmptyItem = async (schema: Schema, parentId?: string) => {
    const emptyItem: Record<string, any> = {};

    if (schema?.schema_definition?.fields) {
      for (const field of schema.schema_definition.fields) {
        if (field.default !== undefined) {
          emptyItem[field.name] = field.default;
        } else if (field.type === "boolean") {
          emptyItem[field.name] = false;
        } else if (field.type === "integer" || field.type === "decimal") {
          emptyItem[field.name] = field.validation?.min || 0;
        } else if (field.type === "text") {
          emptyItem[field.name] = "";
        }

        // Handle auto-population based on schema configuration
        if (field?.auto_populate && parentId) {
          const autoPopulate = field.auto_populate;

          if (autoPopulate.source === "parent_context") {
            emptyItem[field.name] = parentId;
          }
        }
      }
    }

    return emptyItem;
  };

  const getDisplayValue = (item: Record<string, any>) => {
    // Try to find a suitable display field
    const displayField =
      relationship.displayField ||
      relatedSchema?.schema_definition?.fields?.find(
        (f: Field) => f.name === relationship.sourceField
      )?.ui_config?.display_field ||
      relationship.sourceField ||
      "name";

    if (displayField.includes(".")) {
      const key_list = displayField.split(".");
      let value = item;
      for (const key of key_list) {
        if (!value) {
          return "";
        }
        value = value[key];
      }
      return value;
    }

    if (item[displayField]) {
      return item[displayField];
    }

    // Check for relationship fields that might only contain IDs
    // Look for fields that end with "_id" and have corresponding display values
    const relationshipFields = relatedSchema?.schema_definition?.fields?.filter(
      (f: Field) => f.foreign_key && f.name.endsWith("_id")
    ) || [];

    for (const field of relationshipFields) {
      const fieldValue = item[field.name];
      if (fieldValue) {
        // Try to find a corresponding display field for this relationship
        const displayFieldName = field.ui_config?.display_field;
        if (displayFieldName) {
          // Check if we have the display value stored alongside the ID
          const relatedFieldName = field.name.replace("_id", "");
          if (item[relatedFieldName] && typeof item[relatedFieldName] === "object") {
            return item[relatedFieldName][displayFieldName] || item[relatedFieldName].name || fieldValue;
          }

          // If we have a direct display field value, use it
          if (item[displayFieldName]) {
            return item[displayFieldName];
          }
        }

        // Fallback: create a meaningful display from the field name and ID
        const fieldLabel = field.ui_config?.label || field.name.replace("_id", "").replace("_", " ");
        return `${fieldLabel}: ${fieldValue}`;
      }
    }

    // Fallback to first text field
    if (relatedSchema?.schema_definition?.fields) {
      const textField = relatedSchema.schema_definition.fields.find(
        (f: Field) => f.type === "text" && f.name !== "id"
      );
      if (textField && item[textField.name]) {
        return item[textField.name];
      }
    }

    return item.id || "New Item";
  };

  return (
    <Card className={`border-l-4 border-l-green-500 ${className}`}>
      <CardContent className="p-0">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800">
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 h-auto"
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            <h3 className="font-semibold text-sm">{relationship.title}</h3>
            <Badge variant="secondary" className="text-xs">
              {item ? "1" : "0"}
            </Badge>
          </div>

          <div className="flex items-center space-x-2">
            {!item && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addItem}
                className="flex items-center space-x-1 text-xs h-7"
              >
                <Plus className="h-3 w-3" />
                <span>Add {relationship.title}</span>
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        {!isCollapsed && (
          <div className="p-4">
            {!item ? (
              <div className="text-center py-8 text-muted-foreground">
                <p className="text-sm">
                  No {relationship.title.toLowerCase()} added yet
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addItem}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add {relationship.title}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Item Display Header */}
                <div className="flex items-center p-3 bg-slate-25 dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">1</span>
                    </div>
                    <span className="text-sm font-medium">
                      {getDisplayValue(item)}
                    </span>
                  </div>
                </div>

                {/* Item Content - Always Visible */}
                {relatedSchema && (
                  <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg bg-white dark:bg-slate-900">
                    <DynamicFieldRenderer
                      schema={relatedSchema}
                      item={item}
                      itemIndex={null}
                      fieldName={fieldName}
                      onUpdate={updateItem}
                      relatedSchemas={relatedSchemas}
                      parentId={parentId}
                      mode="edit"
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};